:root {
  --background: oklch(0.9582 0.0152 90.2357);
  --foreground: oklch(0.3760 0.0225 64.3434);
  --card: oklch(0.9914 0.0098 87.4695);
  --card-foreground: oklch(0.3760 0.0225 64.3434);
  --popover: oklch(0.9914 0.0098 87.4695);
  --popover-foreground: oklch(0.3760 0.0225 64.3434);
  --primary: #0066FF;
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8846 0.0302 85.5655);
  --secondary-foreground: oklch(0.4313 0.0300 64.9288);
  --muted: oklch(0.9239 0.0190 83.0636);
  --muted-foreground: oklch(0.5391 0.0387 71.1655);
  --accent: #0066FF;
  --accent-foreground: oklch(0.3760 0.0225 64.3434);
  --destructive: oklch(0.5471 0.1438 32.9149);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8606 0.0321 84.5881);
  --input: oklch(0.8606 0.0321 84.5881);
  --ring: #0066FF;
  --chart-1: oklch(0.6180 0.0778 65.5444);
  --chart-2: oklch(0.5604 0.0624 68.5805);
  --chart-3: oklch(0.4851 0.0570 72.6827);
  --chart-4: oklch(0.6777 0.0624 64.7755);
  --chart-5: oklch(0.7264 0.0581 66.6967);
  --sidebar: oklch(0.9239 0.0190 83.0636);
  --sidebar-foreground: oklch(0.3760 0.0225 64.3434);
  --sidebar-primary: oklch(0.6180 0.0778 65.5444);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8348 0.0426 88.8064);
  --sidebar-accent-foreground: oklch(0.3760 0.0225 64.3434);
  --sidebar-border: oklch(0.8606 0.0321 84.5881);
  --sidebar-ring: oklch(0.6180 0.0778 65.5444);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --radius: 0.25rem;
  --shadow-2xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-sm: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow-md: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12);
  --shadow-lg: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12);
  --shadow-xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12);
  --shadow-2xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.30);
}

.dark {
  --background: #0A0A0A;
  --foreground: #FFFFFF;
  --card: #1A1A1A;
  --card-foreground: #FFFFFF;
  --popover: #1A1A1A;
  --popover-foreground: #FFFFFF;
  --primary: #0066FF;
  --primary-foreground: #FFFFFF;
  --secondary: #333333;
  --secondary-foreground: #FFFFFF;
  --muted: #2A2A2A;
  --muted-foreground: #A0A0A0;
  --accent: #0066FF;
  --accent-foreground: #FFFFFF;
  --destructive: #FF4444;
  --destructive-foreground: #FFFFFF;
  --border: #333333;
  --input: #333333;
  --ring: #0066FF;
  --chart-1: oklch(0.7264 0.0581 66.6967);
  --chart-2: oklch(0.6777 0.0624 64.7755);
  --chart-3: oklch(0.6180 0.0778 65.5444);
  --chart-4: oklch(0.5604 0.0624 68.5805);
  --chart-5: oklch(0.4851 0.0570 72.6827);
  --sidebar: oklch(0.2747 0.0139 57.6523);
  --sidebar-foreground: oklch(0.9239 0.0190 83.0636);
  --sidebar-primary: oklch(0.7264 0.0581 66.6967);
  --sidebar-primary-foreground: oklch(0.2747 0.0139 57.6523);
  --sidebar-accent: oklch(0.4186 0.0281 56.3404);
  --sidebar-accent-foreground: oklch(0.9239 0.0190 83.0636);
  --sidebar-border: oklch(0.3795 0.0181 57.1280);
  --sidebar-ring: oklch(0.7264 0.0581 66.6967);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --radius: 0.25rem;
  --shadow-2xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-xs: 2px 3px 5px 0px hsl(28 13% 20% / 0.06);
  --shadow-sm: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 1px 2px -1px hsl(28 13% 20% / 0.12);
  --shadow-md: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 2px 4px -1px hsl(28 13% 20% / 0.12);
  --shadow-lg: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 4px 6px -1px hsl(28 13% 20% / 0.12);
  --shadow-xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.12), 2px 8px 10px -1px hsl(28 13% 20% / 0.12);
  --shadow-2xl: 2px 3px 5px 0px hsl(28 13% 20% / 0.30);
}