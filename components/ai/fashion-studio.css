/* AI Fashion Studio Styles - Adapted to Project Theme System */

.fashion-studio {
  min-height: 100vh;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans);
}

.fashion-studio-container {
  display: flex;
  height: 100vh;
}

/* Left Sidebar - Control Panel */
.fashion-studio-sidebar {
  width: 320px;
  background: linear-gradient(180deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  border-right: 1px solid hsl(var(--border));
  display: flex;
  flex-direction: column;
}

.fashion-studio-header {
  padding: 1.5rem;
  border-bottom: 1px solid hsl(var(--border));
}

.fashion-studio-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

.fashion-studio-subtitle {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.fashion-studio-controls {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Upload Sections */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.upload-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.upload-zone {
  border: 2px dashed hsl(var(--border));
  border-radius: var(--radius);
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: hsl(var(--background));
}

.upload-zone:hover {
  border-color: hsl(var(--primary));
  background: hsl(var(--primary) / 0.05);
}

.upload-zone.dragover {
  border-color: hsl(var(--primary));
  background: hsl(var(--primary) / 0.1);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.upload-icon {
  width: 3rem;
  height: 3rem;
  color: hsl(var(--muted-foreground));
}

.upload-text {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  margin: 0;
}

.upload-hint {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground) / 0.7);
  margin: 0;
}

.upload-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.preview-image {
  width: 100%;
  height: 8rem;
  object-fit: cover;
  border-radius: var(--radius);
}

.remove-btn {
  font-size: 0.75rem;
  color: hsl(var(--destructive));
}

/* Settings Section */
.settings-section {
  padding-top: 1rem;
  border-top: 1px solid hsl(var(--border));
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.slider-container {
  position: relative;
}

.intensity-slider {
  width: 100%;
  height: 0.5rem;
  background: hsl(var(--muted));
  border-radius: var(--radius);
  appearance: none;
  cursor: pointer;
}

.intensity-slider::-webkit-slider-thumb {
  appearance: none;
  width: 1rem;
  height: 1rem;
  background: hsl(var(--primary));
  border-radius: 50%;
  cursor: pointer;
}

.slider-value {
  position: absolute;
  top: -2rem;
  left: 75%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.quality-buttons {
  display: flex;
  gap: 0.5rem;
}

.quality-btn {
  flex: 1;
  font-size: 0.75rem;
}

/* Generate Button */
.generate-btn {
  width: 100%;
  margin-top: auto;
}

/* Main Content Area */
.fashion-studio-main {
  flex: 1;
  background: radial-gradient(circle at center, hsl(var(--card)) 0%, hsl(var(--background)) 100%);
  display: flex;
  flex-direction: column;
}

.main-header {
  padding: 1.5rem;
  border-bottom: 1px solid hsl(var(--border));
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.main-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: hsl(var(--primary));
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.main-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.main-content {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-container {
  width: 100%;
  max-width: 48rem;
  position: relative;
}

.preview-container .generated-image,
.preview-placeholder {
  aspect-ratio: 16/9;
  background: hsl(var(--muted));
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.generated-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.placeholder-icon {
  width: 4rem;
  height: 4rem;
  color: hsl(var(--muted-foreground));
}

.placeholder-text {
  color: hsl(var(--muted-foreground));
  margin: 0;
}

.placeholder-hint {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground) / 0.7);
  margin: 0;
}

.loading-overlay {
  position: absolute;
  inset: 0;
  background: hsl(var(--background) / 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius);
}

.loading-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid hsl(var(--primary));
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: hsl(var(--foreground));
  margin: 0;
}

.progress-bar {
  width: 12rem;
  height: 0.25rem;
  background: hsl(var(--muted));
  border-radius: var(--radius);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  animation: progress 2s infinite;
}

.preview-info {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.info-left,
.info-right {
  display: flex;
  gap: 1rem;
}

/* Right Sidebar - History */
.fashion-studio-history {
  width: 288px;
  background: linear-gradient(180deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  border-left: 1px solid hsl(var(--border));
  display: flex;
  flex-direction: column;
}

.history-header {
  padding: 1.5rem;
  border-bottom: 1px solid hsl(var(--border));
}

.history-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

.history-subtitle {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.history-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.history-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  background: hsl(var(--background));
  border-radius: var(--radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.history-item:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.history-item.selected {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

.history-image-container {
  aspect-ratio: 1;
  background: hsl(var(--muted));
  position: relative;
}

.history-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.history-info {
  padding: 0.75rem;
}

.history-time {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin: 0 0 0.25rem 0;
}

.history-title-text {
  font-size: 0.875rem;
  color: hsl(var(--foreground));
  margin: 0;
}

.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  gap: 0.5rem;
}

.empty-icon {
  width: 2rem;
  height: 2rem;
  color: hsl(var(--muted-foreground));
}

.empty-text {
  color: hsl(var(--muted-foreground));
  margin: 0;
}

.empty-hint {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground) / 0.7);
  margin: 0;
}

.history-footer {
  padding: 1rem;
  border-top: 1px solid hsl(var(--border));
}

.view-all-btn {
  width: 100%;
  font-size: 0.875rem;
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes progress {
  0% { width: 0%; }
  100% { width: 100%; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .fashion-studio-container {
    flex-direction: column;
    height: auto;
  }
  
  .fashion-studio-sidebar,
  .fashion-studio-history {
    width: 100%;
  }
  
  .fashion-studio-main {
    min-height: 60vh;
  }
}

/* Scrollbar Styling */
.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.history-content::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
