<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Fashion Studio - Professional Fashion Tech Platform</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0066FF',
                        secondary: '#1A1A1A'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #0A0A0A;
            color: #FFFFFF;
            overflow-x: hidden;
        }
        
        .upload-zone {
            border: 2px dashed #333333;
            transition: all 0.3s ease;
        }
        
        .upload-zone:hover {
            border-color: #0066FF;
            background: rgba(0, 102, 255, 0.05);
        }
        
        .upload-zone.dragover {
            border-color: #0066FF;
            background: rgba(0, 102, 255, 0.1);
        }
        
        .thumbnail-item {
            transition: all 0.2s ease;
        }
        
        .thumbnail-item:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 20px rgba(0, 102, 255, 0.2);
        }
        
        .thumbnail-item.selected {
            border: 2px solid #0066FF;
            box-shadow: 0 0 0 4px rgba(0, 102, 255, 0.2);
        }
        
        .main-stage {
            background: radial-gradient(circle at center, #1A1A1A 0%, #141414 100%);
        }
        
        .control-panel, .history-panel {
            background: linear-gradient(180deg, #1A1A1A 0%, #161616 100%);
        }
        
        .generated-image {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #0066FF 0%, #0080FF 100%);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .status-indicator {
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 4px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #1A1A1A;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #333333;
            border-radius: 2px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #0066FF;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="flex h-screen">
        <!-- Left Column - Control Panel -->
        <div class="w-80 control-panel border-r border-gray-800 flex flex-col">
            <div class="p-6 border-b border-gray-800">
                <h1 class="text-xl font-semibold text-white mb-2">AI Fashion Studio</h1>
                <p class="text-sm text-gray-400">Professional Fashion Generation</p>
            </div>
            
            <div class="flex-1 p-6 space-y-6">
                <!-- Reference Image Upload -->
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-gray-300">Reference Image (Model)</label>
                    <div class="upload-zone rounded-lg p-6 text-center cursor-pointer" id="referenceUpload">
                        <div class="w-12 h-12 mx-auto mb-3 flex items-center justify-center text-gray-500">
                            <i class="ri-image-add-line ri-2x"></i>
                        </div>
                        <p class="text-sm text-gray-400 mb-2">Drop your reference image here</p>
                        <p class="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                        <input type="file" class="hidden" id="referenceInput" accept="image/*">
                    </div>
                    <div class="hidden" id="referencePreview">
                        <img class="w-full h-32 object-cover rounded-lg" alt="Reference preview">
                        <button class="mt-2 text-xs text-red-400 hover:text-red-300" id="removeReference">Remove</button>
                    </div>
                </div>
                
                <!-- Garment Image Upload -->
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-gray-300">Garment Image</label>
                    <div class="upload-zone rounded-lg p-6 text-center cursor-pointer" id="garmentUpload">
                        <div class="w-12 h-12 mx-auto mb-3 flex items-center justify-center text-gray-500">
                            <i class="ri-shirt-line ri-2x"></i>
                        </div>
                        <p class="text-sm text-gray-400 mb-2">Drop your garment image here</p>
                        <p class="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                        <input type="file" class="hidden" id="garmentInput" accept="image/*">
                    </div>
                    <div class="hidden" id="garmentPreview">
                        <img class="w-full h-32 object-cover rounded-lg" alt="Garment preview">
                        <button class="mt-2 text-xs text-red-400 hover:text-red-300" id="removeGarment">Remove</button>
                    </div>
                </div>
                
                <!-- Generation Settings -->
                <div class="space-y-4 pt-4 border-t border-gray-800">
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-300">Style Intensity</label>
                        <div class="relative">
                            <input type="range" min="0" max="100" value="75" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer" id="intensitySlider">
                            <div class="absolute -top-8 left-3/4 transform -translate-x-1/2 text-xs text-gray-400" id="intensityValue">75%</div>
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-300">Quality</label>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs rounded-button bg-gray-700 text-gray-300 hover:bg-gray-600 quality-btn" data-quality="standard">Standard</button>
                            <button class="px-3 py-1 text-xs rounded-button bg-primary text-white quality-btn" data-quality="high">High</button>
                            <button class="px-3 py-1 text-xs rounded-button bg-gray-700 text-gray-300 hover:bg-gray-600 quality-btn" data-quality="ultra">Ultra</button>
                        </div>
                    </div>
                </div>
                
                <!-- Generate Button -->
                <button class="w-full bg-primary hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-button transition-colors whitespace-nowrap" id="generateBtn">
                    <span class="flex items-center justify-center">
                        <i class="ri-magic-line mr-2"></i>
                        Generate Fashion
                    </span>
                </button>
            </div>
        </div>
        
        <!-- Center Column - Main Stage -->
        <div class="flex-1 main-stage flex flex-col">
            <div class="p-6 border-b border-gray-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-lg font-medium text-white">Generation Preview</h2>
                        <div class="flex items-center space-x-2 text-sm text-gray-400">
                            <div class="w-2 h-2 bg-green-500 rounded-full status-indicator"></div>
                            <span>Ready</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="p-2 text-gray-400 hover:text-white rounded-button hover:bg-gray-800">
                            <i class="ri-download-line"></i>
                        </button>
                        <button class="p-2 text-gray-400 hover:text-white rounded-button hover:bg-gray-800">
                            <i class="ri-share-line"></i>
                        </button>
                        <button class="p-2 text-gray-400 hover:text-white rounded-button hover:bg-gray-800">
                            <i class="ri-more-line"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="flex-1 p-6 flex items-center justify-center">
                <div class="w-full max-w-2xl">
                    <div class="aspect-[16/9] bg-gray-900 rounded-xl generated-image overflow-hidden relative">
                        <div class="absolute inset-0 flex items-center justify-center" id="placeholderContent">
                            <div class="text-center">
                                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center text-gray-600">
                                    <i class="ri-image-line ri-3x"></i>
                                </div>
                                <p class="text-gray-400 mb-2">Upload images to start generating</p>
                                <p class="text-sm text-gray-500">Your AI-generated fashion will appear here</p>
                            </div>
                        </div>
                        <img class="w-full h-full object-cover hidden" id="generatedImage" alt="Generated fashion">
                        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden" id="loadingOverlay">
                            <div class="text-center">
                                <div class="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                                <p class="text-white mb-2">Generating your fashion...</p>
                                <div class="w-48 h-1 bg-gray-700 rounded-full mx-auto">
                                    <div class="h-full progress-bar rounded-full" style="width: 0%" id="progressBar"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 flex items-center justify-between text-sm text-gray-400">
                        <div class="flex items-center space-x-4">
                            <span>Resolution: 1024 × 576</span>
                            <span>Format: PNG</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span>Generation time: --</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column - History Gallery -->
        <div class="w-72 history-panel border-l border-gray-800 flex flex-col">
            <div class="p-6 border-b border-gray-800">
                <h3 class="text-lg font-medium text-white mb-2">Generation History</h3>
                <p class="text-sm text-gray-400">Recent creations</p>
            </div>
            
            <div class="flex-1 p-4 overflow-y-auto scrollbar-thin">
                <div class="space-y-3" id="historyGrid">
                    <!-- History items will be populated here -->
                    <div class="thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer selected">
                        <div class="aspect-square bg-gray-700 relative">
                            <img src="https://readdy.ai/api/search-image?query=Professional%20fashion%20model%20wearing%20elegant%20black%20dress%2C%20studio%20lighting%2C%20minimalist%20background%2C%20high-end%20fashion%20photography%2C%20clean%20aesthetic%2C%20modern%20style&width=400&height=400&seq=1&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                        </div>
                        <div class="p-3">
                            <p class="text-xs text-gray-400 mb-1">2 minutes ago</p>
                            <p class="text-sm text-gray-300">Elegant Evening Dress</p>
                        </div>
                    </div>
                    
                    <div class="thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer">
                        <div class="aspect-square bg-gray-700 relative">
                            <img src="https://readdy.ai/api/search-image?query=Fashion%20model%20in%20casual%20streetwear%2C%20modern%20urban%20style%2C%20professional%20photography%2C%20clean%20background%2C%20contemporary%20fashion%2C%20stylish%20outfit&width=400&height=400&seq=2&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                        </div>
                        <div class="p-3">
                            <p class="text-xs text-gray-400 mb-1">15 minutes ago</p>
                            <p class="text-sm text-gray-300">Casual Streetwear</p>
                        </div>
                    </div>
                    
                    <div class="thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer">
                        <div class="aspect-square bg-gray-700 relative">
                            <img src="https://readdy.ai/api/search-image?query=Professional%20business%20attire%20fashion%20model%2C%20formal%20wear%2C%20corporate%20style%2C%20studio%20photography%2C%20clean%20minimalist%20background%2C%20sophisticated%20outfit&width=400&height=400&seq=3&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                        </div>
                        <div class="p-3">
                            <p class="text-xs text-gray-400 mb-1">1 hour ago</p>
                            <p class="text-sm text-gray-300">Business Formal</p>
                        </div>
                    </div>
                    
                    <div class="thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer">
                        <div class="aspect-square bg-gray-700 relative">
                            <img src="https://readdy.ai/api/search-image?query=Summer%20fashion%20model%20in%20light%20casual%20wear%2C%20bright%20natural%20lighting%2C%20fresh%20clean%20background%2C%20seasonal%20clothing%2C%20comfortable%20style&width=400&height=400&seq=4&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                        </div>
                        <div class="p-3">
                            <p class="text-xs text-gray-400 mb-1">2 hours ago</p>
                            <p class="text-sm text-gray-300">Summer Collection</p>
                        </div>
                    </div>
                    
                    <div class="thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer">
                        <div class="aspect-square bg-gray-700 relative">
                            <img src="https://readdy.ai/api/search-image?query=Luxury%20fashion%20model%20in%20designer%20outfit%2C%20high-end%20couture%2C%20premium%20styling%2C%20professional%20studio%20photography%2C%20elegant%20sophisticated%20look&width=400&height=400&seq=5&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                            <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                        </div>
                        <div class="p-3">
                            <p class="text-xs text-gray-400 mb-1">3 hours ago</p>
                            <p class="text-sm text-gray-300">Designer Couture</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="p-4 border-t border-gray-800">
                <button class="w-full text-sm text-gray-400 hover:text-white py-2 text-center">
                    View All History
                </button>
            </div>
        </div>
    </div>

    <script id="upload-functionality">
        document.addEventListener('DOMContentLoaded', function() {
            const referenceUpload = document.getElementById('referenceUpload');
            const referenceInput = document.getElementById('referenceInput');
            const referencePreview = document.getElementById('referencePreview');
            const removeReference = document.getElementById('removeReference');
            
            const garmentUpload = document.getElementById('garmentUpload');
            const garmentInput = document.getElementById('garmentInput');
            const garmentPreview = document.getElementById('garmentPreview');
            const removeGarment = document.getElementById('removeGarment');
            
            function setupUpload(uploadZone, input, preview, removeBtn) {
                uploadZone.addEventListener('click', () => input.click());
                
                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.classList.add('dragover');
                });
                
                uploadZone.addEventListener('dragleave', () => {
                    uploadZone.classList.remove('dragover');
                });
                
                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadZone.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        handleFileUpload(files[0], uploadZone, preview);
                    }
                });
                
                input.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleFileUpload(e.target.files[0], uploadZone, preview);
                    }
                });
                
                removeBtn.addEventListener('click', () => {
                    uploadZone.classList.remove('hidden');
                    preview.classList.add('hidden');
                    input.value = '';
                });
            }
            
            function handleFileUpload(file, uploadZone, preview) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    uploadZone.classList.add('hidden');
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
            
            setupUpload(referenceUpload, referenceInput, referencePreview, removeReference);
            setupUpload(garmentUpload, garmentInput, garmentPreview, removeGarment);
        });
    </script>

    <script id="generation-controls">
        document.addEventListener('DOMContentLoaded', function() {
            const intensitySlider = document.getElementById('intensitySlider');
            const intensityValue = document.getElementById('intensityValue');
            const qualityBtns = document.querySelectorAll('.quality-btn');
            const generateBtn = document.getElementById('generateBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const progressBar = document.getElementById('progressBar');
            const placeholderContent = document.getElementById('placeholderContent');
            const generatedImage = document.getElementById('generatedImage');
            
            intensitySlider.addEventListener('input', (e) => {
                intensityValue.textContent = e.target.value + '%';
                intensityValue.style.left = (e.target.value / 100 * 100) + '%';
            });
            
            qualityBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    qualityBtns.forEach(b => {
                        b.classList.remove('bg-primary', 'text-white');
                        b.classList.add('bg-gray-700', 'text-gray-300');
                    });
                    btn.classList.remove('bg-gray-700', 'text-gray-300');
                    btn.classList.add('bg-primary', 'text-white');
                });
            });
            
            generateBtn.addEventListener('click', () => {
                startGeneration();
            });
            
            function startGeneration() {
                placeholderContent.classList.add('hidden');
                loadingOverlay.classList.remove('hidden');
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<span class="flex items-center justify-center"><i class="ri-loader-4-line mr-2 animate-spin"></i>Generating...</span>';
                
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        setTimeout(completeGeneration, 500);
                    }
                    progressBar.style.width = progress + '%';
                }, 200);
            }
            
            function completeGeneration() {
                loadingOverlay.classList.add('hidden');
                generatedImage.src = 'https://readdy.ai/api/search-image?query=Professional%20fashion%20model%20wearing%20stylish%20contemporary%20outfit%2C%20high-end%20fashion%20photography%2C%20studio%20lighting%2C%20clean%20minimalist%20background%2C%20elegant%20pose%2C%20modern%20design%20aesthetic&width=1024&height=576&seq=main&orientation=landscape';
                generatedImage.classList.remove('hidden');
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<span class="flex items-center justify-center"><i class="ri-magic-line mr-2"></i>Generate Fashion</span>';
                
                addToHistory();
            }
            
            function addToHistory() {
                const historyGrid = document.getElementById('historyGrid');
                const newItem = document.createElement('div');
                newItem.className = 'thumbnail-item bg-gray-800 rounded-lg overflow-hidden cursor-pointer';
                newItem.innerHTML = `
                    <div class="aspect-square bg-gray-700 relative">
                        <img src="https://readdy.ai/api/search-image?query=Professional%20fashion%20model%20wearing%20stylish%20contemporary%20outfit%2C%20high-end%20fashion%20photography%2C%20studio%20lighting%2C%20clean%20minimalist%20background%2C%20elegant%20pose%2C%20modern%20design%20aesthetic&width=400&height=400&seq=new&orientation=squarish" class="w-full h-full object-cover object-top" alt="Fashion generation">
                        <div class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200"></div>
                    </div>
                    <div class="p-3">
                        <p class="text-xs text-gray-400 mb-1">Just now</p>
                        <p class="text-sm text-gray-300">New Generation</p>
                    </div>
                `;
                
                document.querySelectorAll('.thumbnail-item').forEach(item => {
                    item.classList.remove('selected');
                });
                
                newItem.classList.add('selected');
                historyGrid.insertBefore(newItem, historyGrid.firstChild);
            }
        });
    </script>

    <script id="history-interaction">
        document.addEventListener('DOMContentLoaded', function() {
            const historyGrid = document.getElementById('historyGrid');
            const generatedImage = document.getElementById('generatedImage');
            const placeholderContent = document.getElementById('placeholderContent');
            
            historyGrid.addEventListener('click', (e) => {
                const thumbnailItem = e.target.closest('.thumbnail-item');
                if (thumbnailItem) {
                    document.querySelectorAll('.thumbnail-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    thumbnailItem.classList.add('selected');
                    
                    const img = thumbnailItem.querySelector('img');
                    if (img) {
                        placeholderContent.classList.add('hidden');
                        generatedImage.src = img.src.replace('400x400', '1024x576').replace('squarish', 'landscape');
                        generatedImage.classList.remove('hidden');
                    }
                }
            });
        });
    </script>
</body>
</html>