import { getUserUuid } from "@/services/user";
import { redirect } from "next/navigation";
import FashionStudioGenerator from "@/components/ai/fashion-studio-generator";

export default async function FashionStudioPage() {
  const userUuid = await getUserUuid();

  const callbackUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/fashion-studio`;
  if (!userUuid) {
    redirect(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
  }

  return (
    <div className="min-h-screen">
      <FashionStudioGenerator
        provider="tuzi"
        model="flux-kontext-pro"
      />
    </div>
  );
}
