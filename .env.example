# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "ShipAny"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = ""
SUPABASE_ANON_KEY = ""
SUPABASE_SERVICE_ROLE_KEY = ""

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
# https://plausible.io/
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = ""

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# Google Adsence Code
# https://adsense.com/
NEXT_PUBLIC_GOOGLE_ADCODE = ""

# -----------------------------------------------------------------------------
# Credit Strategy Configuration
# -----------------------------------------------------------------------------
# Credit allocation strategy: default, geolocation, ab_test
CREDIT_STRATEGY = default

# -----------------------------------------------------------------------------
# Geolocation-based Credits Configuration
# Requires Cloudflare IP Geolocation to be enabled
# Only used when CREDIT_STRATEGY=geolocation
# -----------------------------------------------------------------------------
# Enable/disable geolocation-based credit allocation
GEO_CREDITS_ENABLED = true

# Tier 1: High-value countries (developed markets)
GEO_CREDITS_TIER1_COUNTRIES = "US,CA,GB,DE,FR,AU,JP,KR,NL,SE,NO,DK,CH"
GEO_CREDITS_TIER1_AMOUNT = 100

# Tier 2: Medium-value countries (emerging markets)
GEO_CREDITS_TIER2_COUNTRIES = "BR,MX,RU,TR,ZA,AR,CL,TH,MY,PH"
GEO_CREDITS_TIER2_AMOUNT = 50

# Tier 3: High-risk countries (abuse prevention)
GEO_CREDITS_TIER3_COUNTRIES = "IN,BD,PK,NG,EG,VN,ID,LK"
GEO_CREDITS_TIER3_AMOUNT = 20

# Default credits for other countries
GEO_CREDITS_DEFAULT_AMOUNT = 75

# -----------------------------------------------------------------------------
# Image Processing Configuration
# -----------------------------------------------------------------------------
# Image processing provider: cloudflare, self-hosted
IMAGE_PROCESSING_PROVIDER = cloudflare

# General image settings
IMAGE_DEFAULT_QUALITY = 85
IMAGE_DEFAULT_FORMAT = auto
IMAGE_MAX_SIZE = ********
IMAGE_ALLOWED_FORMATS = jpeg,png,webp,avif

# Cloudflare Images configuration (when IMAGE_PROCESSING_PROVIDER=cloudflare)
CLOUDFLARE_ACCOUNT_ID = ""
CLOUDFLARE_API_TOKEN = ""
CLOUDFLARE_IMAGE_DELIVERY_URL = ""
CLOUDFLARE_IMAGE_FEATURES = resize,watermark,format

# Self-hosted image processing (when IMAGE_PROCESSING_PROVIDER=self-hosted)
SELF_HOSTED_IMAGE_ENDPOINT = http://localhost:3001
SELF_HOSTED_IMAGE_API_KEY = ""
SELF_HOSTED_IMAGE_CONCURRENCY = 5
SELF_HOSTED_IMAGE_CACHE = true
SELF_HOSTED_IMAGE_CACHE_TTL = 3600