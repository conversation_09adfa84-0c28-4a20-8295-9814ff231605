# 积分分配策略系统

本文档介绍积分分配策略系统，支持多种积分分配方式，包括基于地理位置的积分策略，用于防止滥用和薅羊毛行为。

## 📋 功能概述

### 系统特点
- **策略模式**: 支持多种积分分配策略，易于扩展
- **配置驱动**: 通过环境变量控制策略选择和参数
- **分层架构**: 清晰的职责分离，符合设计规范
- **向后兼容**: 可随时切换策略，不影响现有功能

### 支持的策略
1. **默认策略**: 固定积分，适用于简单场景
2. **地理位置策略**: 基于用户国家分配积分，防止滥用
3. **A/B测试策略**: 测试不同积分数量的效果

### 问题背景
- **薅羊毛行为**: 某些地区用户大量注册多个账号获取免费积分
- **付费意愿差异**: 不同地区用户的付费转化率存在显著差异
- **成本控制**: 需要根据地区特点调整积分分配策略
- **策略测试**: 需要测试不同积分策略的效果

## 🔧 技术实现

### 1. 策略配置

#### 选择积分策略
```bash
# 积分分配策略选择
CREDIT_STRATEGY=default          # 默认策略 - 固定积分
CREDIT_STRATEGY=geolocation      # 地理位置策略 - 基于国家
CREDIT_STRATEGY=ab_test          # A/B测试策略 - 测试效果
```

#### 策略说明
- **default**: 所有用户获得相同积分 (CreditsAmount.NewUserGet)
- **geolocation**: 根据用户国家分配不同积分
- **ab_test**: 50%用户获得标准积分，50%获得双倍积分

### 2. 地理位置策略配置

#### Cloudflare 配置
1. 登录 Cloudflare Dashboard
2. 选择您的域名
3. 进入 **Network** 设置
4. 开启 **IP Geolocation** 开关

#### 环境变量配置
```bash
# 启用地理位置策略
CREDIT_STRATEGY=geolocation
GEO_CREDITS_ENABLED=true

# 第一层: 高价值市场 (发达国家)
GEO_CREDITS_TIER1_COUNTRIES="US,CA,GB,DE,FR,AU,JP,KR"
GEO_CREDITS_TIER1_AMOUNT=100

# 第二层: 中等市场 (新兴市场)
GEO_CREDITS_TIER2_COUNTRIES="BR,MX,RU,TR,ZA"
GEO_CREDITS_TIER2_AMOUNT=50

# 第三层: 高风险市场 (需要控制滥用)
GEO_CREDITS_TIER3_COUNTRIES="IN,BD,PK,NG,EG"
GEO_CREDITS_TIER3_AMOUNT=20

# 默认积分 (其他国家)
GEO_CREDITS_DEFAULT_AMOUNT=75
```

#### 验证配置
- 访问 `/api/geolocation/test` 测试地理位置检测
- 访问 `/api/admin/credit-strategy` 查看策略配置

### 3. 分层策略

#### Tier 1 - 高价值市场 (100积分)
- **国家**: 美国、加拿大、英国、德国、法国、澳大利亚、日本、韩国等
- **特点**: 发达国家，高付费意愿，低滥用风险
- **策略**: 给予标准积分，鼓励试用

#### Tier 2 - 中等市场 (50积分)
- **国家**: 巴西、墨西哥、俄罗斯、土耳其、南非等
- **特点**: 新兴市场，中等付费意愿
- **策略**: 适度减少积分，平衡体验和成本

#### Tier 3 - 高风险市场 (20积分)
- **国家**: 印度、孟加拉国、巴基斯坦、尼日利亚、埃及等
- **特点**: 高滥用风险，低付费转化率
- **策略**: 最少积分，重点防止滥用

#### Default - 其他国家 (75积分)
- **国家**: 未明确分类的其他国家
- **策略**: 中等积分，观察数据后调整

## 🛡️ 反滥用机制

### 1. 风险评分系统
```typescript
// 风险因素评分
- 高风险国家: +40分
- 中风险国家: +20分
- Tor网络: +60分
- 临时邮箱: +50分
- 可疑User Agent: +40分
```

### 2. 特殊情况处理
- **Tor网络** (`T1`): 高风险，建议人工审核
- **未知国家** (`XX`): 中等风险，给予默认积分
- **VPN检测**: 依赖Cloudflare的IP数据库

### 3. 监控和日志
- 自动记录所有地理位置事件
- 开发环境显示详细信息
- 生产环境可集成分析平台

## 📊 API 接口

### 策略管理接口
```bash
GET /api/admin/credit-strategy
```
查看当前积分策略配置和状态（需要管理员权限）。

### 地理位置测试接口
```bash
GET /api/geolocation/test
```
测试地理位置检测功能，返回当前请求的地理位置信息和风险评估。

### 地理位置配置接口
```bash
GET /api/admin/geolocation/config
```
查看地理位置配置和环境变量状态（需要管理员权限）。

## 🔄 使用流程

### 1. 用户注册
1. 用户提交注册信息
2. 系统从 `CF-IPCountry` header 获取国家代码
3. 根据配置的分层策略确定积分数量
4. 评估注册风险
5. 分配相应积分并记录日志

### 2. 配置调整
1. 修改环境变量
2. 重启应用使配置生效
3. 通过测试接口验证
4. 监控效果并调整

## 📈 效果监控

### 关键指标
- **注册转化率**: 各地区用户注册完成率
- **付费转化率**: 注册用户的付费转化情况
- **滥用检测**: 异常注册行为识别
- **积分使用率**: 不同地区用户的积分消耗模式

### 数据分析
- 定期分析各地区用户行为
- 根据数据调整分层策略
- A/B测试验证策略效果

## ⚠️ 注意事项

### 1. 法律合规
- 确保符合各国反歧视法律
- 在服务条款中说明积分分配规则
- 提供申诉和客服渠道

### 2. 用户体验
- 避免过度限制正常用户
- 提供清晰的积分说明
- 建立公平的申诉机制

### 3. 技术限制
- VPN用户可能绕过地理检测
- IP地理位置数据约95-99%准确
- 移动网络可能存在跨国路由

## 🚀 最佳实践

### 1. 渐进式实施
1. **阶段1**: 先收集数据，不调整积分
2. **阶段2**: 对明确的高风险地区降低积分
3. **阶段3**: 根据数据扩展到其他地区
4. **阶段4**: 建立动态调整机制

### 2. 监控和调整
- 每周检查关键指标
- 每月评估策略效果
- 季度调整分层配置
- 年度全面策略审查

### 3. 风险缓解
- 建立多重验证机制
- 实施设备指纹识别
- 监控异常注册模式
- 提供人工审核流程

## 🔧 故障排除

### 常见问题

#### 1. 地理位置检测不工作
- 检查 Cloudflare IP Geolocation 是否启用
- 确认域名通过 Cloudflare 代理
- 访问测试接口验证 `CF-IPCountry` header

#### 2. 积分分配异常
- 检查环境变量配置
- 验证国家代码格式（大写，逗号分隔）
- 查看应用日志中的错误信息

#### 3. 配置不生效
- 重启应用使环境变量生效
- 检查配置文件语法
- 验证管理员权限设置

### 调试工具
- 使用 `/api/geolocation/test` 测试当前检测结果
- 查看开发环境控制台日志
- 使用 `/api/admin/geolocation/config` 检查配置状态

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基于 Cloudflare 的地理位置检测
- 实现三层积分分配策略
- 添加风险评估机制
- 提供管理和测试接口
