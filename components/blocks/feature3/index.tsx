import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@radix-ui/react-tabs";

import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature3({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section className="py-16">
      <div className="container px-8">
        <div className="mb-16 max-w-xl px-8 lg:px-0">
          {section.label && (
            <Badge variant="outline" className="mb-4">
              {section.label}
            </Badge>
          )}
          <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
            {section.description}
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-3">
          {section.items?.map((item, index) => (
            <div key={index} className="text-center">
              <div className="mb-4 flex justify-center">
                <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                  {item.icon ? (
                    <Icon name={item.icon} className="size-8 text-primary" />
                  ) : (
                    <span className="text-2xl font-bold text-primary">
                      {index + 1}
                    </span>
                  )}
                </div>
              </div>
              <h3 className="mb-3 text-xl font-semibold">
                {item.title}
              </h3>
              <p className="text-muted-foreground">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
