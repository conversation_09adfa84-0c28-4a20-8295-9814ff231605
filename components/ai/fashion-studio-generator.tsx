"use client";

import { useState, useCallback, useRef } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import Icon from "@/components/icon";
import "./fashion-studio.css";

interface FashionStudioGeneratorProps {
  onGenerate?: (result: any) => void;
  provider?: string;
  model?: string;
  defaultOptions?: Record<string, any>;
}

export default function FashionStudioGenerator({
  onGenerate,
  provider = "tuzi",
  model = "flux-kontext-pro",
  defaultOptions = {}
}: FashionStudioGeneratorProps) {
  const [referenceImage, setReferenceImage] = useState<string>("");
  const [garmentImage, setGarmentImage] = useState<string>("");
  const [referenceFile, setReferenceFile] = useState<File | null>(null);
  const [garmentFile, setGarmentFile] = useState<File | null>(null);
  const [styleIntensity, setStyleIntensity] = useState(75);
  const [quality, setQuality] = useState("high");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string>("");
  const [generationHistory, setGenerationHistory] = useState<any[]>([]);
  const [selectedHistoryItem, setSelectedHistoryItem] = useState<number>(0);
  
  const referenceInputRef = useRef<HTMLInputElement>(null);
  const garmentInputRef = useRef<HTMLInputElement>(null);

  // 处理参考图上传
  const handleReferenceUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
      toast.error("Invalid file type. Only JPEG, PNG, and WebP are allowed");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("File too large. Maximum size is 10MB");
      return;
    }

    setReferenceFile(file);
    
    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => setReferenceImage(e.target?.result as string);
    reader.readAsDataURL(file);
  }, []);

  // 处理服装图上传
  const handleGarmentUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
      toast.error("Invalid file type. Only JPEG, PNG, and WebP are allowed");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("File too large. Maximum size is 10MB");
      return;
    }

    setGarmentFile(file);
    
    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => setGarmentImage(e.target?.result as string);
    reader.readAsDataURL(file);
  }, []);

  // 生成时尚图片
  const handleGenerate = useCallback(async () => {
    if (!referenceFile || !garmentFile) {
      toast.error("Please upload both reference and garment images");
      return;
    }

    setIsGenerating(true);
    
    try {
      // 这里应该调用实际的API
      // 模拟生成过程
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 模拟生成结果
      const mockResult = "https://readdy.ai/api/search-image?query=Professional%20fashion%20model%20wearing%20stylish%20contemporary%20outfit%2C%20high-end%20fashion%20photography%2C%20studio%20lighting%2C%20clean%20minimalist%20background%2C%20elegant%20pose%2C%20modern%20design%20aesthetic&width=1024&height=576&seq=main&orientation=landscape";
      
      setGeneratedImage(mockResult);
      
      // 添加到历史记录
      const newHistoryItem = {
        id: Date.now(),
        image: mockResult,
        timestamp: new Date(),
        title: "New Generation"
      };
      
      setGenerationHistory(prev => [newHistoryItem, ...prev]);
      setSelectedHistoryItem(0);
      
      if (onGenerate) {
        onGenerate(mockResult);
      }
      
      toast.success("Fashion image generated successfully!");
      
    } catch (error) {
      console.error("Generation failed:", error);
      toast.error("Generation failed. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  }, [referenceFile, garmentFile, styleIntensity, quality, onGenerate]);

  return (
    <div className="fashion-studio">
      <div className="fashion-studio-container">
        {/* Left Column - Control Panel */}
        <div className="fashion-studio-sidebar">
          <div className="fashion-studio-header">
            <h1 className="fashion-studio-title">AI Fashion Studio</h1>
            <p className="fashion-studio-subtitle">Professional Fashion Generation</p>
          </div>
          
          <div className="fashion-studio-controls">
            {/* Reference Image Upload */}
            <div className="upload-section">
              <Label className="upload-label">Reference Image (Model)</Label>
              <div 
                className="upload-zone"
                onClick={() => referenceInputRef.current?.click()}
              >
                {referenceImage ? (
                  <div className="upload-preview">
                    <Image
                      src={referenceImage}
                      alt="Reference preview"
                      width={200}
                      height={128}
                      className="preview-image"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="remove-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        setReferenceImage("");
                        setReferenceFile(null);
                        if (referenceInputRef.current) referenceInputRef.current.value = "";
                      }}
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <div className="upload-placeholder">
                    <Icon name="RiImageAddLine" className="upload-icon" />
                    <p className="upload-text">Drop your reference image here</p>
                    <p className="upload-hint">PNG, JPG up to 10MB</p>
                  </div>
                )}
                <input
                  ref={referenceInputRef}
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleReferenceUpload}
                />
              </div>
            </div>

            {/* Garment Image Upload */}
            <div className="upload-section">
              <Label className="upload-label">Garment Image</Label>
              <div 
                className="upload-zone"
                onClick={() => garmentInputRef.current?.click()}
              >
                {garmentImage ? (
                  <div className="upload-preview">
                    <Image
                      src={garmentImage}
                      alt="Garment preview"
                      width={200}
                      height={128}
                      className="preview-image"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      className="remove-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        setGarmentImage("");
                        setGarmentFile(null);
                        if (garmentInputRef.current) garmentInputRef.current.value = "";
                      }}
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <div className="upload-placeholder">
                    <Icon name="RiShirtLine" className="upload-icon" />
                    <p className="upload-text">Drop your garment image here</p>
                    <p className="upload-hint">PNG, JPG up to 10MB</p>
                  </div>
                )}
                <input
                  ref={garmentInputRef}
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleGarmentUpload}
                />
              </div>
            </div>

            {/* Generation Settings */}
            <div className="settings-section">
              <div className="setting-group">
                <Label className="setting-label">Style Intensity</Label>
                <div className="slider-container">
                  <Input
                    type="range"
                    min="0"
                    max="100"
                    value={styleIntensity}
                    onChange={(e) => setStyleIntensity(Number(e.target.value))}
                    className="intensity-slider"
                  />
                  <div className="slider-value">{styleIntensity}%</div>
                </div>
              </div>
              
              <div className="setting-group">
                <Label className="setting-label">Quality</Label>
                <div className="quality-buttons">
                  {["standard", "high", "ultra"].map((q) => (
                    <Button
                      key={q}
                      variant={quality === q ? "default" : "outline"}
                      size="sm"
                      onClick={() => setQuality(q)}
                      className="quality-btn"
                    >
                      {q.charAt(0).toUpperCase() + q.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerate}
              disabled={!referenceFile || !garmentFile || isGenerating}
              className="generate-btn"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Icon name="RiLoader4Line" className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Icon name="RiMagicLine" className="w-4 h-4 mr-2" />
                  Generate Fashion
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Center Column - Main Stage */}
        <div className="fashion-studio-main">
          <div className="main-header">
            <div className="main-title-section">
              <h2 className="main-title">Generation Preview</h2>
              <div className="status-indicator">
                <div className="status-dot"></div>
                <span className="status-text">Ready</span>
              </div>
            </div>
            <div className="main-actions">
              <Button variant="ghost" size="sm">
                <Icon name="RiDownloadLine" className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Icon name="RiShareLine" className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Icon name="RiMoreLine" className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="main-content">
            <div className="preview-container">
              {generatedImage ? (
                <Image
                  src={generatedImage}
                  alt="Generated fashion"
                  width={1024}
                  height={576}
                  className="generated-image"
                />
              ) : (
                <div className="preview-placeholder">
                  <Icon name="RiImageLine" className="placeholder-icon" />
                  <p className="placeholder-text">Upload images to start generating</p>
                  <p className="placeholder-hint">Your AI-generated fashion will appear here</p>
                </div>
              )}
              
              {isGenerating && (
                <div className="loading-overlay">
                  <div className="loading-content">
                    <div className="loading-spinner"></div>
                    <p className="loading-text">Generating your fashion...</p>
                    <div className="progress-bar">
                      <div className="progress-fill"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div className="preview-info">
              <div className="info-left">
                <span>Resolution: 1024 × 576</span>
                <span>Format: PNG</span>
              </div>
              <div className="info-right">
                <span>Generation time: --</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - History Gallery */}
        <div className="fashion-studio-history">
          <div className="history-header">
            <h3 className="history-title">Generation History</h3>
            <p className="history-subtitle">Recent creations</p>
          </div>
          
          <div className="history-content">
            <div className="history-grid">
              {generationHistory.length > 0 ? (
                generationHistory.map((item, index) => (
                  <div
                    key={item.id}
                    className={`history-item ${selectedHistoryItem === index ? 'selected' : ''}`}
                    onClick={() => {
                      setSelectedHistoryItem(index);
                      setGeneratedImage(item.image);
                    }}
                  >
                    <div className="history-image-container">
                      <Image
                        src={item.image}
                        alt="Fashion generation"
                        width={400}
                        height={400}
                        className="history-image"
                      />
                    </div>
                    <div className="history-info">
                      <p className="history-time">
                        {index === 0 ? "Just now" : `${index * 15} minutes ago`}
                      </p>
                      <p className="history-title-text">{item.title}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="history-empty">
                  <Icon name="RiImageLine" className="empty-icon" />
                  <p className="empty-text">No generations yet</p>
                  <p className="empty-hint">Your generated images will appear here</p>
                </div>
              )}
            </div>
          </div>
          
          <div className="history-footer">
            <Button variant="ghost" className="view-all-btn">
              View All History
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
